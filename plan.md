1. auth.module.ts
Avoid registering both entities and custom repositories in the same forFeature call
TypeOrmModule.forFeature is intended to receive only entities (and, since v8, custom repositories that extend DataSource). Mixing both can lead to duplicated provider tokens (<EntityName>Repository) and circular-dependency errors at runtime.

If your custom repositories are decorated with @EntityRepository / @Injectable, register them in a separate forFeature or convert them to the new DataSource.getRepository() pattern. Example refactor:

@Module({
  imports: [
    …
-   TypeOrmModule.forFeature([
-     ApiKey, ApiKeyRepository,
-     AppInstallation, AppInstallationRepository,
-     User, UserRepository,
-   ]),
+   // 1️⃣ entities
+   TypeOrmModule.forFeature([ApiKey, AppInstallation, User]),
+   // 2️⃣ custom repos
+   TypeOrmModule.forFeature([ApiKeyRepository, AppInstallationRepository, UserRepository]),
    …
  ],
})


2.  privileged-app-auth.strategy.ts
Avoid re-parsing the API key – pass the validated bot user instead
getUserInSameOrganization(apiKey, userIdToImpersonate) forces the service to re-validate or parse the key again. You already have botUser; passing its uid/orgId eliminates duplicate DB work and failure points.

-const validationResult = await this.privilegedAppService.getUserInSameOrganization(
-  apiKey,
-  userIdToImpersonate
-);
+const validationResult = await this.privilegedAppService.getUserInSameOrganization(
+  botUser,
+  userIdToImpersonate,
+);

This is code rabbit suggestion, i would say, then dont validate the apikey first, directly validate in the method, dont pass the botUser, because it may break the functionlity


