import { Injectable, Logger, UnauthorizedException } from "@nestjs/common";
import {
  AuthenticationGrpcClient,
  BaseAuthStrategy,
  IHttpAuthStrategy,
  User,
} from "@repo/nestjs-commons/guards";
import { FastifyRequest } from "fastify";
import { PrivilegedAppService } from "../services/privileged-app.service";

@Injectable()
export class PrivilegedAppAuthStrategy
  extends BaseAuthStrategy
  implements IHttpAuthStrategy
{
  private readonly logger = new Logger(PrivilegedAppAuthStrategy.name);

  constructor(
    private readonly authClient: AuthenticationGrpcClient,
    private readonly privilegedAppService: PrivilegedAppService,
  ) {
    super();
  }

  /**
   * Authenticates the user using the privileged app strategy with user impersonation.
   * This strategy is used when both X-api-key and x-user-id headers are present.
   * @param request The request object.
   * @returns A promise that resolves to a User object (the impersonated user).
   */
  async authenticate(request: FastifyRequest): Promise<User> {
    const apiKey = request.headers["x-api-key"] as string;
    const userIdToImpersonate = request.headers["x-user-id"] as string;

    if (!apiKey) {
      throw new UnauthorizedException("API key is required");
    }

    if (!userIdToImpersonate) {
      throw new UnauthorizedException("x-user-id header is required for privileged app authentication");
    }

    // Validate the privileged app impersonation (includes API key validation)
    const validationResult = await this.privilegedAppService.getUserInSameOrganization(
      apiKey,
      userIdToImpersonate
    );

    if (!validationResult.isValid) {
      this.logger.warn('Privileged app authentication failed', {
        error: validationResult.error,
        botUserId: botUser.sub,
        requestedUserId: userIdToImpersonate,
        orgId: botUser.orgId,
      });
      throw new UnauthorizedException(validationResult.error);
    }

    let userName = 'unknown';
    if (validationResult.user.email) {
      try {
        userName = validationResult.user.email.split("@")[0];
      } catch (error) {
        this.logger.warn('Email format incorrect during user impersonation', {
          email: validationResult.user.email,
          userId: validationResult.user.id,
          error: error.message,
        });
        userName = 'unknown';
      }
    } else {
      this.logger.warn('Email missing during user impersonation', {
        userId: validationResult.user.id,
      });
    }

    let orgUid = '';
    let orgTier = '';
    if (validationResult.user.organization) {
      orgUid = validationResult.user.organization.uid || '';
      orgTier = validationResult.user.organization.tier || '';
    } else {
      this.logger.warn('Organization data missing during user impersonation', {
        userId: validationResult.user.id,
        organizationId: validationResult.user.organizationId,
      });
    }

    const impersonatedUser: User = {
      authId: validationResult.user.authId || '',
      sub: validationResult.user.id,
      uid: validationResult.user.uid,
      email: validationResult.user.email || '',
      userName,
      userType: validationResult.user.userType,
      scopes: botUser.scopes,
      orgId: validationResult.user.organizationId,
      orgUid,
      orgTier,
      metadata: {
        ...validationResult.user.metadata,
        userType: validationResult.user.userType,
        timezone: validationResult.user.timezone || 'UTC',
        // Add impersonation metadata for audit purposes
        impersonation: {
          isImpersonated: true,
          originalBotUserUid: validationResult.metadata.originalBotUserUid,
          impersonatedUserUid: validationResult.metadata.impersonatedUserUid,
          appUid: validationResult.metadata.appUid,
          organizationUid: validationResult.metadata.organizationUid,
          impersonatedAt: new Date().toISOString(),
        },
      },
      token: botUser.token,
    };

    this.logger.log('Privileged app authentication successful', {
      botUserUid: botUser.uid,
      impersonatedUserUid: impersonatedUser.uid,
      appUid: validationResult.metadata.appUid,
    });

    return this.standardizeUser(impersonatedUser);
  }

  /**
   * Checks if this strategy should be used for the given request.
   * Returns true if both X-api-key and x-user-id headers are present and not empty.
   */
  static shouldUse(request: FastifyRequest): boolean {
    const hasApiKey = !!request.headers['x-api-key'];
    const hasUserId = !!request.headers['x-user-id'];
    return hasApiKey && hasUserId;
  }
}
